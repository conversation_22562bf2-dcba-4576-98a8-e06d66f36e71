<template>
  <a-select
    v-bind="$attrs"
    v-model="selectedValue"
    :loading="isLoading"
    :filter-option="false"
    :placeholder="placeholder || '姓名/电话/实体卡号'"
    allow-search
    allow-clear
    @change="handleUserChange"
    @search="getSearchUserList"
  >
    <a-option v-for="user in userList" :key="user.user_id" :value="user.user_id">
      {{ user.username }} ({{ user.phone }})
    </a-option>
    <template #empty>
      <a-empty :description="emptyDes" />
    </template>
  </a-select>
</template>

<script lang="ts" setup>
  /* 所有场馆会员搜索组件 */
  import debounce from 'lodash/debounce';
  import { searchAllUserInfo } from '@/api/front-money';
  import { isChinese } from '@/utils';

  const props = defineProps<{
    placeholder?: string;
    modelValue?: string;
    busId?: string;
  }>();

  const emits = defineEmits(['update:modelValue', 'change']);
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const { isLoading, execute: getUser } = searchAllUserInfo();
  const emptyDes = ref('暂无数据');
  const userList = ref<Record<string, any>[]>([]);
  const getSearchUserList = debounce(async (search: string) => {
    if (!isChinese(search)) {
      emptyDes.value = '至少输入3位数字';
      userList.value = [];
      return;
    }

    emptyDes.value = '暂无数据';
    if (search) {
      const { data } = await getUser({
        data: {
          search: search.trim(),
          bus_id: props.busId || '',
        },
      });
      userList.value = data.value || [];
    } else {
      userList.value = [];
    }
  }, 400);
  const handleUserChange = (value: any) => {
    const userInfo = userList.value.find((item) => item.user_id === value);
    emits('change', userInfo);
  };
</script>
