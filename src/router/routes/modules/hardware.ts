import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const HARDWARE: AppRouteRecordRaw = {
  path: '/hardware',
  name: 'Hardware',
  meta: {
    locale: '硬件',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'yps-list',
      name: 'YpsList',
      component: () => import('@/views/hardware/yps-list.vue'),
      meta: {
        locale: '英派斯器械列表',
        parentName: 'Hardware',
      },
    },
    {
      path: 'yps-device',
      name: 'YpsDevice',
      component: () => import('@/views/hardware/yps-device.vue'),
      meta: {
        locale: '添加器械',
        parentName: 'YpsList',
      },
    },
    {
      path: 'yps-device/:id',
      name: 'YpsDeviceEdit',
      component: () => import('@/views/hardware/yps-device.vue'),
      meta: {
        locale: '编辑器械',
        parentName: 'YpsList',
      },
    },
    {
      path: 'defender',
      name: 'Defender',
      component: () => import('@/views/hardware/defender.vue'),
      meta: {
        locale: '防尾随检查',
        parentName: 'Hardware',
      },
    },
  ],
};

export default HARDWARE;
