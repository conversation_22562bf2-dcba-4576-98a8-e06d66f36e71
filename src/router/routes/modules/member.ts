import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const MEMBER: AppRouteRecordRaw = {
  path: '/member',
  name: 'Member',
  meta: {
    locale: '会员',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'black-list',
      name: 'MerchantBlackList',
      component: () => import('@/views/black-list/list.vue'),
      meta: {
        keepAlive: true,
        locale: '会员黑名单',
      },
    },
    {
      path: 'black-card-list',
      name: 'MerchantBlackCardList',
      component: () => import('@/views/black-card-list/list.vue'),
      meta: {
        keepAlive: true,
        locale: '会员卡禁用',
      },
    },
    {
      path: 'merchant-list',
      name: 'MemberMerchantList',
      component: () => import('@/views/member/merchant-list.vue'),
      meta: {
        keepAlive: true,
        locale: '会员列表',
      },
    },
    {
      path: 'followup-list',
      name: 'MemberFollowupList',
      component: () => import('@/views/member/followup-list.vue'),
      meta: {
        keepAlive: true,
        locale: '跟进列表',
        parentName: 'Member',
      },
    },
    {
      path: 'followup-detail',
      name: 'MemberFollowupDetail',
      component: () => import('@/views/member/followup-detail.vue'),
      meta: {
        keepAlive: true,
        locale: '跟进明细',
        parentName: 'MemberFollowupList',
      },
    },
  ],
};

export default MEMBER;
