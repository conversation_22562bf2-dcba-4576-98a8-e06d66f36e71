<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="memberSettings"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="显示会员动态" field="is_open_member_dynamic">
          <a-switch v-model="memberSettings.is_open_member_dynamic" />
          <template #extra>旧版会员端展示会员动态模块</template>
        </a-form-item>
        <a-form-item label="积分查看" field="is_open_member_point">
          <a-switch v-model="memberSettings.is_open_member_point" />
        </a-form-item>
        <a-form-item label="体测记录查看" field="is_open_stamina">
          <a-switch v-model="memberSettings.is_open_stamina" />
        </a-form-item>
        <a-form-item label="显示购卡协议" field="is_open_buycard_protocol">
          <a-switch v-model="memberSettings.is_open_buycard_protocol" />
          <a-link
            v-if="memberSettings.is_open_buycard_protocol"
            style="margin-left: 8px"
            :disabled="!isEdit"
            @click="isShowProtocolEditModal = true">
            编辑内容
          </a-link>
        </a-form-item>
        <a-form-item label="显示团课预约协议" field="is_open_reservation_protocol">
          <a-switch v-model="memberSettings.is_open_reservation_protocol" />
        </a-form-item>
        <a-form-item label="发票开具查看" field="is_open_invoice">
          <a-switch v-model="memberSettings.is_open_invoice" />
        </a-form-item>
        <a-form-item label="教练评价">
          <a-switch v-model="courseComment.COACH_RATING_SWITCH.value" />
          <a-link
            v-if="courseComment.COACH_RATING_SWITCH.value"
            style="margin-left: 8px"
            :disabled="!isEdit"
            @click="isShowCoachCommentModal = true">
            编辑内容
          </a-link>
        </a-form-item>
        <a-form-item label="团课评价">
          <a-switch v-model="groupClassComment.GROUP_CLASS_EVALUATION_SWITCH.value" />
          <a-link
            v-if="groupClassComment.GROUP_CLASS_EVALUATION_SWITCH.value"
            style="margin-left: 8px"
            :disabled="!isEdit"
            @click="isShowGroupClassCommentModal = true">
            编辑内容
          </a-link>
        </a-form-item>
        <a-form-item label="人脸上传">
          <a-select v-model="memberSettings.member_upload_face_limit" style="width: 100%" :allow-clear="false">
            <a-option value="-99">不能上传</a-option>
            <a-option value="0">不限制</a-option>
            <a-option value="7">购卡后7天允许上传</a-option>
            <a-option value="15">购卡后15天允许上传</a-option>
            <a-option value="30">购卡后30天允许上传</a-option>
            <a-option value="90">购卡后90天允许上传</a-option>
            <a-option value="-1">每次需授权上传</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="会员入场智能提醒" :content-flex="false">
          <div style="margin: 6px 0 12px">首页提醒&进店指引提醒</div>
          <a-space>
            <a-checkbox v-model="memberSettings.check_active_card">会籍卡状态激活</a-checkbox>
            <a-checkbox v-model="memberSettings.check_user_face">人脸上传</a-checkbox>
            <a-checkbox v-model="memberSettings.check_user_palmservice">微信刷掌</a-checkbox>
            <a-checkbox v-model="memberSettings.check_order_sign">待签合同</a-checkbox>
          </a-space>
          <div style="margin: 6px 0 12px">券码核销&购卡成功提醒</div>
          <a-space>
            <a-checkbox v-model="memberSettings.check_success_active_card">会籍卡状态激活</a-checkbox>
            <a-checkbox v-model="memberSettings.check_success_user_face">人脸上传</a-checkbox>
            <a-checkbox v-model="memberSettings.check_success_user_palmservice">微信刷掌</a-checkbox>
            <a-checkbox v-model="memberSettings.check_success_order_sign">待签合同</a-checkbox>
          </a-space>
          <template #extra>开启后，系统将检查新会员是否具备入场条件，若发现无法入场的时候，会弹窗提醒会员。</template>
        </a-form-item>
        <a-form-item label="确认购卡门店弹窗">
          <a-switch v-model="memberSettings.check_bus_buy_card" />
          <template #extra>开启后，会员购买通卡时将弹窗提醒会员确认购买门店。</template>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
    <member-protocol-modal v-model="isShowProtocolEditModal" />
    <coach-comment-modal
      v-model="isShowCoachCommentModal"
      :init-data="courseComment"
      :boolean-keys-comment="booleanKeysComment"
      @on-ok="onCoachCommentOk" />
    <group-class-comment-modal
      v-model="isShowGroupClassCommentModal"
      :init-data="groupClassComment"
      :boolean-keys-comment="booleanKeysComment"
      @on-ok="onGroupClassCommentOk" />
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getSetting, updateSetting } from '@/api/member-setting';
  import MemberProtocolModal from './member-protocol-modal.vue';
  import CoachCommentModal from './coach-comment-modal.vue';
  import GroupClassCommentModal from './group-class-comment-modal.vue';

  const memberSettings = reactive({
    member_is_upload_face: '1',
    member_upload_face_limit: '0',
    is_open_member_dynamic: true,
    is_open_member_point: true,
    is_open_stamina: true,
    is_open_buycard_protocol: false,
    is_open_reservation_protocol: false,
    is_open_invoice: false,
    check_active_card: true,
    check_user_face: true,
    check_user_palmservice: true,
    check_order_sign: true,
    check_bus_buy_card: true,
    check_success_user_face: false,
    check_success_order_sign: false,
    check_success_active_card: false,
    check_success_user_palmservice: false,
  });

  const isShowProtocolEditModal = ref(false);
  const isShowCoachCommentModal = ref(false);
  const isShowGroupClassCommentModal = ref(false);
  // 教练评价
  const courseComment = reactive({
    COACH_RATING_SWITCH: {
      id: '',
      value: true,
    },
  });
  // --团课评价--
  const groupClassComment = reactive({
    GROUP_CLASS_EVALUATION_SWITCH: {
      id: '',
      value: true,
    },
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = updateSetting();
  const formRef = ref<FormInstance>();
  const booleanKeys = [
    'is_open_member_dynamic',
    'is_open_member_point',
    'is_open_stamina',
    'is_open_buycard_protocol',
    'is_open_reservation_protocol',
    'is_open_invoice',
    'check_active_card',
    'check_user_face',
    'check_user_palmservice',
    'check_order_sign',
    'check_bus_buy_card',
    'check_success_user_face',
    'check_success_order_sign',
    'check_success_active_card',
    'check_success_user_palmservice',
  ];
  const booleanKeysComment = ['COACH_RATING_SWITCH', 'GROUP_CLASS_EVALUATION_SWITCH'];
  async function getInfo() {
    const res = await getSetting();
    const resData = res.data.value;

    const resDataInfo = resData.info;
    const resDataComment = resData.course_comment;
    Object.entries(resDataInfo).forEach(([key, val]) => {
      if (booleanKeys.includes(key)) {
        memberSettings[key] = val === '1';
      }
    });
    memberSettings.member_is_upload_face = resDataInfo.member_is_upload_face;
    if (resDataInfo.member_is_upload_face === '0') {
      memberSettings.member_upload_face_limit = '-99';
    } else {
      memberSettings.member_upload_face_limit = resDataInfo.member_upload_face_limit;
    }
    Object.entries(resDataComment).forEach(([key, val]) => {
      if (key.indexOf('COACH_') !== -1) {
        courseComment[key] = booleanKeysComment.includes(key) ? { ...val, value: val.value === '1' } : val;
      }
      if (key.indexOf('GROUP_CLASS_') !== -1) {
        groupClassComment[key] = booleanKeysComment.includes(key) ? { ...val, value: val.value === '1' } : val;
      }
    });
  }
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      const info = {
        ...memberSettings,
        member_is_upload_face: memberSettings.member_upload_face_limit === '-99' ? '0' : 1,
        member_upload_face_limit:
          memberSettings.member_upload_face_limit === '-99' ? '0' : memberSettings.member_upload_face_limit,
      };
      const courseCommentData = JSON.parse(
        JSON.stringify({
          ...groupClassComment,
          ...courseComment,
        })
      );
      Object.entries(info).forEach(([key, val]) => {
        if (booleanKeys.includes(key)) {
          info[key] = val ? '1' : '0';
        }
      });
      Object.entries(courseCommentData).forEach(([key, val]) => {
        if (booleanKeysComment.includes(key)) {
          courseCommentData[key].value = val.value ? '1' : '0';
        }
      });
      setInfo({
        data: {
          ...info,
          courseComment: courseCommentData,
        },
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
        getInfo();
      });
    }
  };
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  function onCoachCommentOk(data) {
    Object.assign(courseComment, data);
  }
  function onGroupClassCommentOk(data) {
    Object.assign(groupClassComment, data);
  }
  getInfo();
</script>

<style lang="less" scoped></style>
