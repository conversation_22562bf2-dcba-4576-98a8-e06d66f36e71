<template>
  <!-- Search Form -->
  <a-card class="general-card">
    <a-form
      :model="searchForm"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
      label-align="left"
      auto-label-width>
      <a-row :gutter="16">
        <a-col :flex="1">
          <a-form-item field="date_range" label="时间">
            <DateRangePicker
              v-model:model-value="searchForm.date_range"
              v-model:time-range="searchForm.time_range"
              :max-range-months="6"
              max-range-message="日期范围不能超过6个月，请重新选择" />
          </a-form-item>
        </a-col>
        <a-col :flex="1">
          <a-form-item field="marketers_id" label="会籍">
            <SalesSelect
              v-model="searchForm.marketers_id"
              :belong-bus-id="searchForm.bus_id"
              label-in-value
              style="width: 300px"
              is-membership
              :is-coach="false"
              placeholder="请选择"></SalesSelect>
          </a-form-item>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-form-item>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="24" style="display: flex; align-items: center; justify-content: flex-end">
          <a-button @click="handleExport">导出Excel</a-button>
        </a-col>
      </a-row>
    </a-form>

    <!-- Table Section -->
    <a-table
      :loading="loading"
      :data="tableData"
      :pagination="pagination"
      :bordered="false"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange">
      <template #columns>
        <a-table-column title="序号" data-index="index" :width="80" align="center">
          <template #cell="{ rowIndex }">
            {{ (pagination.page_no - 1) * pagination.page_size + rowIndex + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="会籍名称" data-index="marketers_name" align="center" />
        <a-table-column title="跟进人数" data-index="follow_user_num" align="center">
          <template #cell="{ record }">
            <span style="color: #1890ff; cursor: pointer" @click="handleViewMembers(record)">
              {{ record.follow_user_num }}
            </span>
          </template>
        </a-table-column>
        <a-table-column title="跟进次数" data-index="follow_num" align="center">
          <template #cell="{ record }">
            <span style="color: #1890ff; cursor: pointer" @click="handleViewTimes(record)">
              {{ record.follow_num }}
            </span>
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="100" align="center">
          <template #cell="{ record }">
            <a-link @click="handleViewDetail(record)">详情</a-link>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import dayjs from 'dayjs';
  import { useRouter } from 'vue-router';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import { useBusInfoStore } from '@/store';
  import SalesSelect from '@/components/membership/salesSelect.vue';
  import DateRangePicker from '@/components/form/date-range-picker.vue';
  import { getFollowupSalespersonList, exportFollowupSalespersonData } from '@/api/followup';

  // 响应式数据
  const loading = ref(false);
  const tableData = ref<any[]>([]);

  const busInfo = useBusInfoStore();
  const router = useRouter();

  const startOfMonth = dayjs().startOf('month').format('YYYY-MM-DD');
  const today = dayjs().format('YYYY-MM-DD');
  // 搜索表单
  const searchForm = reactive<any>({
    date_range: [startOfMonth, today],
    time_range: '',
    marketers_id: '',
    bus_id: busInfo.bus_id,
  });

  // 分页配置
  const pagination = reactive({
    page_no: 1,
    page_size: 10,
    total: 0,
    showPageSize: true,
    showTotal: true,
  });

  // API 实例
  const { execute: executeGetList } = getFollowupSalespersonList();
  const { execute: executeExport } = exportFollowupSalespersonData();

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const params = {
        start_time: searchForm.date_range[0] || '',
        end_time: searchForm.date_range[1] || '',
        marketers_id: searchForm.marketers_id,
        page_no: pagination.page_no,
        page_size: pagination.page_size,
      };

      const { data } = await executeGetList({ data: params });

      if (data.value) {
        tableData.value = data.value.list;
        pagination.total = data.value.count;
      }
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleSearch = async () => {
    pagination.page_no = 1;
    await fetchData();
  };

  const handleExport = async () => {
    try {
      const params = {
        start_time: searchForm.date_range[0] || '',
        end_time: searchForm.date_range[1] || '',
        marketers_id: searchForm.marketers_id,
        _export: 1,
      };

      const { response }: any = await executeExport({ data: params });
      // 导出成功的处理逻辑
      if (response.value?.errorcode === 0) {
        Message.success('导出任务运行中，请稍后到消息中心下载!');
      } else {
        Message.error(response.value?.errormsg);
      }
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  const handlePageChange = (page: number) => {
    pagination.page_no = page;
    fetchData();
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.page_size = pageSize;
    pagination.page_no = 1;
    fetchData();
  };

  const handleViewMembers = (record: any) => {
    // 跳转到跟进人数详情页面并传递 date_range
    router.push({
      name: 'MemberFollowupDetail',
      query: {
        salespersonId: record.marketers_id,
        start_date: searchForm.date_range[0],
        end_date: searchForm.date_range[1],
      },
    });
  };

  const handleViewTimes = (record: any) => {
    // 跳转到跟进次数详情页面并传递 date_range
    router.push({
      name: 'MemberFollowupDetail',
      query: {
        salespersonId: record.marketers_id,
        start_date: searchForm.date_range[0],
        end_date: searchForm.date_range[1],
      },
    });
  };

  const handleViewDetail = (record: any) => {
    // 跳转到详情页面并传递 date_range
    router.push({
      name: 'MemberFollowupDetail',
      query: {
        salespersonId: record.marketers_id,
        start_date: searchForm.date_range[0],
        end_date: searchForm.date_range[1],
      },
    });
  };

  // 初始化
  onMounted(() => {
    fetchData();
  });
</script>

<style scoped lang="less"></style>
