<template>
  <div class="base-box">
    <Breadcrumb />

    <a-card class="general-card">
      <a-tabs :active-key="activeKey" @change="activeKey = $event">
        <a-tab-pane key="1">
          <template #title>
            <!-- <icon-user /> -->
            教练跟进记录
          </template>
          <keep-alive>
            <FollowupCoachList />
          </keep-alive>
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #title>
            <!-- <icon-user-add /> -->
            会籍跟进记录
          </template>
          <keep-alive>
            <FollowupSalespersonList v-if="activeKey === '2'" />
          </keep-alive>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import FollowupCoachList from './components/followup-coach-list.vue';
  import FollowupSalespersonList from './components/followup-salesperson-list.vue';

  const activeKey = ref<any>('1');
  onMounted(() => {
    console.log('onMounted:', activeKey.value);
  });
</script>

<style lang="less" scoped>
  .base-box {
    padding: 0 20px 20px 20px;
  }
</style>
