<template>
  <div class="base-box">
    <Breadcrumb />
    <!-- Search Form -->
    <a-card class="general-card">
      <a-form
        :model="searchForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width>
        <a-row :gutter="16">
          <a-col :flex="1">
            <a-form-item field="date_range" label="时间">
              <DateRangePicker
                v-model:model-value="searchForm.date_range"
                v-model:time-range="searchForm.time_range"
                :max-range-months="6"
                max-range-message="日期范围不能超过6个月，请重新选择" />
            </a-form-item>
          </a-col>
          <a-col :flex="1">
            <a-form-item field="user_id" label="会员昵称">
              <UserSearchAll v-model="searchForm.user_id" :bus-id="searchForm.bus_id" />
            </a-form-item>
          </a-col>
          <a-col v-if="isCoach" :flex="1">
            <a-form-item field="coach_id" label="教练">
              <SalesSelect
                v-model="searchForm.coach_id"
                :belong-bus-id="searchForm.bus_id"
                label-in-value
                style="width: 300px"
                :is-membership="false"
                is-pt-coach
                is-swim-coach
                show-coach-type
                placeholder="请选择"></SalesSelect>
            </a-form-item>
          </a-col>
          <a-col v-if="isSalesperson" :flex="1">
            <a-form-item field="marketers_id" label="会籍">
              <SalesSelect
                v-model="searchForm.marketers_id"
                :belong-bus-id="searchForm.bus_id"
                label-in-value
                style="width: 300px"
                is-membership
                :is-coach="false"
                placeholder="请选择"></SalesSelect>
            </a-form-item>
          </a-col>
          <a-divider style="height: 32px" direction="vertical" />
          <a-col :flex="'86px'" style="text-align: right">
            <a-form-item>
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <icon-search />
                </template>
                搜索
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider style="margin-top: 0" />
        <a-row style="margin-bottom: 16px">
          <a-col :span="24" style="display: flex; align-items: center; justify-content: flex-end">
            <a-button @click="handleExport">导出Excel</a-button>
          </a-col>
        </a-row>
      </a-form>

      <!-- Table Section -->
      <a-table
        :loading="loading"
        :data="tableData"
        :pagination="pagination"
        :bordered="false"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange">
        <template #columns>
          <a-table-column title="序号" data-index="index" :width="80" align="center">
            <template #cell="{ rowIndex }">
              {{ (pagination.page_no - 1) * pagination.page_size + rowIndex + 1 }}
            </template>
          </a-table-column>
          <a-table-column title="会员姓名" data-index="username" align="center" />
          <a-table-column title="跟进内容" data-index="contents" align="center" />
          <a-table-column title="跟进时间" data-index="followup_time" align="center" />
          <a-table-column v-if="isCoach" title="跟进人" data-index="coach_name" align="center" />
          <a-table-column v-if="isSalesperson" title="跟进人" data-index="marketers_name" align="center" />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import { useRoute } from 'vue-router';
  import { useBusInfoStore } from '@/store';
  import SalesSelect from '@/components/membership/salesSelect.vue';
  import DateRangePicker from '@/components/form/date-range-picker.vue';
  import UserSearchAll from '@/components/user/user-search-all.vue';
  import {
    getFollowupCoachDetailList,
    getFollowupSalespersonDetailList,
    exportFollowupCoachDetailData,
    exportFollowupSalespersonDetailData,
    type FollowupDetailRecord,
    type FollowupDetailSearchParams,
  } from '@/api/followup';

  // 定义接口类型
  interface SearchForm {
    date_range: string[];
    time_range: string;
    coach_id: string;
    marketers_id: string;
    bus_id: string;
    user_id: string;
  }

  // 响应式数据
  const loading = ref(false);
  const tableData = ref<FollowupDetailRecord[]>([]);

  const busInfo = useBusInfoStore();
  const route = useRoute();

  const coachId = ref('');
  const salespersonId = ref('');

  const isCoach = computed(() => {
    return !!coachId.value;
  });
  const isSalesperson = computed(() => {
    return !!salespersonId.value;
  });

  const startOfMonth = dayjs().startOf('month').format('YYYY-MM-DD');
  const today = dayjs().format('YYYY-MM-DD');
  // 搜索表单
  const searchForm = reactive<SearchForm>({
    date_range: [startOfMonth, today],
    time_range: '',
    coach_id: '',
    marketers_id: '',
    bus_id: busInfo.bus_id,
    user_id: '',
  });

  // 分页配置
  const pagination = reactive({
    page_no: 1,
    page_size: 10,
    total: 0,
    showPageSize: true,
    showTotal: true,
  });

  // API 实例
  const { execute: executeGetList } = isCoach.value ? getFollowupCoachDetailList() : getFollowupSalespersonDetailList();
  const { execute: executeExport } = isCoach.value
    ? exportFollowupCoachDetailData()
    : exportFollowupSalespersonDetailData();

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const params: FollowupDetailSearchParams = {
        coach_id: searchForm.coach_id,
        marketers_id: searchForm.marketers_id,
        start_time: searchForm.date_range[0] || '',
        end_time: searchForm.date_range[1] || '',
        page_no: pagination.page_no,
        page_size: pagination.page_size,
        user_id: searchForm.user_id,
      };

      const { data } = await executeGetList({ data: params });

      if (data.value) {
        tableData.value = data.value.list;
        pagination.total = data.value.count;
      }
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleSearch = async () => {
    pagination.page_no = 1;
    await fetchData();
  };

  const handleExport = async () => {
    try {
      const params: FollowupDetailSearchParams = {
        coach_id: searchForm.coach_id,
        marketers_id: searchForm.marketers_id,
        start_time: searchForm.date_range[0] || '',
        end_time: searchForm.date_range[1] || '',
        _export: 1,
      };

      const { response }: any = await executeExport({ data: params });
      if (response.value?.errorcode === 0) {
        Message.success('导出任务运行中，请稍后到消息中心下载!');
      } else {
        Message.error(response.value?.errormsg);
      }
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  const handlePageChange = (page: number) => {
    pagination.page_no = page;
    fetchData();
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.page_size = pageSize;
    pagination.page_no = 1;
    fetchData();
  };

  // 初始化
  onMounted(() => {
    // 自动设置 date_range（如果有传递）
    if (route.query.start_date && route.query.end_date) {
      try {
        const startDate = route.query.start_date as string;
        const endDate = route.query.end_date as string;
        searchForm.date_range = [startDate, endDate];
      } catch (e) {
        // ignore
      }
    }
    if (route.query.coachId) {
      coachId.value = route.query.coachId as string;
      searchForm.coach_id = coachId.value;
      salespersonId.value = '';
    }
    if (route.query.salespersonId) {
      salespersonId.value = route.query.salespersonId as string;
      searchForm.marketers_id = salespersonId.value;
      coachId.value = '';
    }
    fetchData();
  });
</script>

<style scoped lang="less"></style>
