<template>
  <a-modal
    v-model:visible="isShowModal"
    title="预约会员"
    :width="720"
    :mask-closable="false"
    :on-before-ok="hansleConfirm">
    <a-form ref="formRef" class="base-set-form" :model="formData" :style="{ width: '100%' }" auto-label-width>
      <a-form-item label="会员名称" field="user_id" :rules="{ required: true, message: '请选择会员' }">
        <UserSearchNew
          v-if="isShowModal"
          v-model="formData.user_id"
          :from="6"
          :bus-id="busId"
          @change="handleUserChange" />
      </a-form-item>
      <a-spin :loading="isLoading">
        <template v-if="showAddInput">
          <a-form-item label="预约用卡" field="card_user_id">
            <a-select v-model="formData.card_user_id" @change="handleCardChange">
              <a-option v-for="item in allChooseList" :key="item.card_user_id" :value="item.card_user_id">
                {{ item.card_name }} {{ getPriceDes(item) }}
              </a-option>
            </a-select>
            <template v-if="addMarkWarning" #extra>
              {{ addMarkWarning }}
            </template>
          </a-form-item>
          <a-form-item label="预约人数" field="num" :rules="{ required: true, message: '请选择预约人数' }">
            <a-input-number v-model="formData.num" :min="1" :max="maxReserveNum" />
          </a-form-item>
          <a-form-item
            v-if="isShowModal && currentInfo.seats_list && currentInfo.seats_list.length"
            label="选座"
            field="seats"
            :rules="{ required: true, message: '请选择座位' }">
            <SeatsSet
              v-model="currentInfo.seats_list"
              style="width: 100%"
              :choose-number="formData.num"
              @on-choose="chooseSeat" />
          </a-form-item>
          <a-form-item v-if="selectedCard.pay_type === 2 || selectedCard.card_type_id === 3" label="总计">
            <a-statistic :value="totalPrice" :precision="2" :value-style="{ color: 'red' }">
              <template #suffix>元</template>
            </a-statistic>
          </a-form-item>
          <a-form-item
            v-if="+totalPrice > 0 && isShowPayTypeList"
            label="支付方式"
            field="new_pay_type"
            :rules="[{ required: true, message: '请选择支付方式' }, { validator: checkTotalPrice }]">
            <PayTypeList
              v-model="formData.new_pay_type"
              :amount="totalPrice"
              :bus-id="formData.bus_id"
              :user-id="formData.user_id"
              :is-lock-card="isLockCard"
              :show-card-pay="selectedCard.card_type_id === 3 && selectedCard.pay_type === 1"
              :sqb-option="{ describe: '团课预约', serviceType: 5, isEqual: false }" />
          </a-form-item>
        </template>
      </a-spin>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getUserMarkCards, addMark, checkLimitCard } from '@/api/class-mark';
  import UserSearchNew from '@/components/user/user-search-new.vue';
  import SeatsSet from '@/components/seats-set/index.vue';
  import PayTypeList from '@/components/form/PayTypeList.vue';

  const props = defineProps<{
    modelValue: boolean;
    currentClass: Record<string, any>;
    busId: string;
  }>();
  const emits = defineEmits(['update:modelValue', 'confirm']);
  const formRef = ref();
  const INIT_DATA = {
    card_user_id: '',
    card_id: '',
    new_pay_type: [],
    user_id: '',
    card_type_id: '',
    pay_type: 1, // pay_type 1卡内余额支付 2微信支付
    num: 1,
    seats: '',
    todeduct: '',
  };
  const INIT_CARD = {
    card_name: '',
    card_type_id: null,
    card_user_id: null,
    maxresv_num: 1,
    pay_type: 1,
    todeduct: 0,
  };
  const isShowPayTypeList = ref(false);
  const formData = reactive({
    ...INIT_DATA,
    course_schedule_id: '',
    bus_id: '',
  });
  watch(
    () => props.currentClass.course_schedule_id,
    (value) => {
      formData.course_schedule_id = value || '';
    },
    { immediate: true }
  );
  watch(
    () => props.busId,
    (value) => {
      formData.bus_id = value || '';
    },
    { immediate: true }
  );
  const selectedCard = reactive({ ...INIT_CARD });
  const maxReserveNum = computed(() => {
    const num = Number(props.currentClass.reserve_number) - Number(props.currentClass.sncount);
    return Math.min(Math.max(num, 1), selectedCard.maxresv_num || Infinity);
  });
  const isLockCard = ref(false);
  const totalPrice = computed(() => {
    if (selectedCard.card_type_id !== 3 && selectedCard.pay_type === 1) {
      return 0;
    }
    const price = Number(((formData.num || 0) * (selectedCard.todeduct || 0)).toFixed(2));
    formData.amount = price;
    return price;
  });
  watch(
    () => totalPrice.value,
    (value) => {
      // 价格变化，重置支付信息 如果当前是储值卡抵扣 默认选中对应的储值卡
      if (selectedCard.card_type_id === 3 && selectedCard.pay_type === 1) {
        isLockCard.value = true;
        formData.new_pay_type = [
          {
            pay_type: 8, // 储值卡支付
            card_user_id: `${selectedCard.card_user_id}`,
            amount: value,
          },
        ];
      } else {
        formData.new_pay_type = [];
        isLockCard.value = false;
      }
    }
  );

  const currentInfo = ref<Record<string, any>>({});
  watch(
    () => props.currentClass,
    (info) => {
      formData.course_schedule_id = info.schedule_id;
      currentInfo.value = info;
    },
    { immediate: true }
  );

  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const showAddInput = ref(false);
  const cardList = ref([]);
  const addMarkWarning = ref('');
  function initFormData() {
    Object.assign(formData, { ...INIT_DATA });
    showAddInput.value = false;
    addMarkWarning.value = '';
    totalPrice.value = 0;
    cardList.value = [];
    Object.assign(selectedCard, { ...INIT_CARD });
  }
  watch(
    () => props.modelValue,
    (value) => {
      if (!value) {
        initFormData();
      }
    }
  );

  watch(
    () => formData.new_pay_type,
    (value, oldValue) => {
      formRef.value.validateField('new_pay_type');
    },
    { deep: true }
  );
  const checkTotalPrice = (value, callback: (arg0?: string) => void) => {
    const totalPay = formData.new_pay_type.reduce((total, item) => total + parseFloat(item.amount), 0).toFixed(2);
    if (Number(totalPay) !== totalPrice.value) {
      callback('支付金额与总金额不一致!');
    } else {
      callback();
    }
  };
  const { isLoading, execute: getUserMarkCardsInfo } = getUserMarkCards();
  const allChooseList = ref([]);

  function checkLimit(info: Record<string, any>) {
    if (!info.card_user_id) {
      addMarkWarning.value = '';
      return;
    }
    checkLimitCard({
      bus_id: formData.bus_id,
      card_user_id: info.card_user_id,
      course_schedule_id: formData.course_schedule_id,
    })
      .then((res) => {
        if (res.response.value.errorcode === 0) {
          addMarkWarning.value = res.response.value.errormsg;
        } else {
          addMarkWarning.value = '';
        }
      })
      .catch((err) => {
        if (err.errorcode === 40001) {
          addMarkWarning.value = err.errormsg;
        } else {
          addMarkWarning.value = '';
        }
      });
  }

  // 选中卡变化
  function afterCardChange(info: Record<string, any> | undefined) {
    isShowPayTypeList.value = false;
    if (!info) {
      info = { ...INIT_CARD };
      isShowPayTypeList.value = true;
    }
    // 防止后端没有返回maxresv_num属性的情况
    info.maxresv_num = info.maxresv_num || null;
    Object.assign(selectedCard, info);
    checkLimit(selectedCard);
    formData.pay_type = info.pay_type;
    formData.card_id = info.card_id;
    formData.card_user_id = info.card_user_id;
    formData.card_type_id = info.card_type_id;
    formData.todeduct = info.todeduct;
    isShowPayTypeList.value = true;
  }
  function handleCardChange(id: string) {
    const info = allChooseList.value.find((item) => item.card_user_id === id);
    afterCardChange(info);
  }
  function getAllChooseList() {
    const preArr =
      props.currentClass.mark_object === '1'
        ? [
            {
              card_user_id: '',
              card_id: '',
              pay_type: 2,
              maxresv_num: null,
              todeduct: props.currentClass.nonmember_price,
              card_name: '无/不使用会员卡',
            },
          ]
        : [];
    allChooseList.value = preArr.concat(cardList.value);
    if (allChooseList.value.length) {
      afterCardChange(
        props.currentClass.mark_object === '1' && allChooseList.value.length > 1
          ? allChooseList.value[1]
          : allChooseList.value[0]
      );
      showAddInput.value = true;
    } else {
      initFormData();
      Message.error('无合适的会员卡');
    }
  }
  function getUserMarkCard() {
    getUserMarkCardsInfo({
      data: { user_id: formData.user_id, course_schedule_id: formData.course_schedule_id, bus_id: formData.bus_id },
    })
      .then((res) => {
        const list = res.data.value;
        cardList.value = list;
        getAllChooseList();
      })
      .catch(() => {
        initFormData();
      });
  }
  function getPriceDes(info) {
    const { card_type_id, pay_type, todeduct, maxresv_num } = info;
    const maxDes = maxresv_num ? `最多约${maxresv_num}人` : '';
    if (pay_type === 1 && card_type_id === 1) {
      return maxDes;
    }
    const typeName = card_type_id === 2 ? '次' : card_type_id === 3 ? '元' : '节';
    return `${maxDes} ${pay_type === 1 ? '' : '支付 '}${todeduct}${pay_type === 1 ? typeName : '元'}/人`;
  }

  // 选中用户变化
  function handleUserChange(info: Record<string, any> | undefined) {
    if (info) {
      formData.user_id = info.user_id || '';
      getUserMarkCard();
    } else {
      initFormData();
    }
    formRef.value.validateField('user_id');
  }
  const { execute: reserve } = addMark();
  async function hansleConfirm() {
    try {
      const errors = await formRef.value.validate();
      if (errors) return false;
      await reserve({ data: formData });
      Message.success('预约成功！');
      formRef.value.resetFields();
      emits('confirm');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
  function chooseSeat(ids) {
    formData.seats = ids;
  }
</script>

<style lang="less" scoped></style>
